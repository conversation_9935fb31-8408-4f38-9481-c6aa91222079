using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Friends;
using Platform.Client.Services.Features.Friends;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.ObjectModel;
using Platform.Framework.Core;

namespace Platform.Client.Common.Features.Friends;

public class FriendProfileFormViewBase : FormBaseMaui<FriendProfileFormBusinessObject, FriendProfileFormViewModel, string, IFriendProfileFormDataService>
{
    public FriendProfileFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }

    public ObservableCollection<string> AvatarOptions { get; set; } = new ObservableCollection<string>();

    protected override async Task OnAppearing()
    {
        await base.OnAppearing();
        
        // Initialize avatar options
        AvatarOptions.Clear();
        for (int i = 1; i <= 12; i++)
        {
            AvatarOptions.Add($"/avatars/{i}.png");
        }
    }
}

public partial class FriendProfileFormView : FriendProfileFormViewBase
{
    public FriendProfileFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
