﻿<?xml version="1.0" encoding="utf-8" ?>
<local:FriendFormViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Add Friend"
    x:DataType="local:FriendFormViewBase"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.NavBarIsVisible="False">

    <Grid BackgroundColor="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}" VerticalOptions="Fill">

        <ScrollView VerticalOptions="Fill">
            <VerticalStackLayout Spacing="8">

                <!--  Main Content Card  -->
                <Border
                    Margin="16"
                    BackgroundColor="{AppThemeBinding Light=WhiteSmoke,
                                                      Dark={StaticResource Gray700}}"
                    Stroke="{AppThemeBinding Light=Gainsboro,
                                             Dark={StaticResource Gray600}}"
                    StrokeThickness="0.5">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="16" />
                    </Border.StrokeShape>
                    <VerticalStackLayout Padding="16,24" Spacing="24">

                        <!--  Header Section with Icon  -->
                        <VerticalStackLayout HorizontalOptions="Center" Spacing="12">
                            <!--  Header Icon  -->
                            <Border
                                Background="{AppThemeBinding Light={StaticResource Gray100},
                                                             Dark={StaticResource Gray600}}"
                                HeightRequest="64"
                                HorizontalOptions="Center"
                                Stroke="{AppThemeBinding Light={StaticResource Gray400},
                                                         Dark={StaticResource Gray500}}"
                                WidthRequest="64">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="32" />
                                </Border.StrokeShape>
                                <Image
                                    HeightRequest="32"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center">
                                    <Image.Source>
                                        <FontImageSource
                                            FontFamily="Jelly"
                                            Glyph="&#xf118;"
                                            Size="16"
                                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray300}}" />
                                    </Image.Source>
                                </Image>
                            </Border>

                            <!--  Header Text  -->
                            <VerticalStackLayout HorizontalOptions="Center" Spacing="8">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="24"
                                    HorizontalTextAlignment="Center"
                                    Text="Add Friend"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                <Label
                                    Margin="8,0"
                                    FontSize="14"
                                    HorizontalTextAlignment="Center"
                                    LineBreakMode="WordWrap"
                                    MaxLines="2"
                                    Text="Enter your friend's invitation code to connect"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                                Dark={StaticResource Gray400}}" />
                            </VerticalStackLayout>
                        </VerticalStackLayout>

                        <!--  Friend Code Input  -->
                        <VerticalStackLayout Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                Text="Friend Invitation Code"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                            <Label
                                Margin="0,0,0,8"
                                FontSize="12"
                                Text="Enter the invitation code shared by your friend"
                                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                            Dark={StaticResource Gray400}}" />
                            <Entry
                                BackgroundColor="Transparent"
                                FontSize="16"
                                Placeholder="Enter invitation code..."
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray500},
                                                                   Dark={StaticResource Gray500}}"
                                Text="{Binding SelectedItem.AuthCode}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />
                        </VerticalStackLayout>



                        <!--  Add Friend Button  -->
                        <Button
                            Margin="0,16,0,0"
                            Command="{Binding SaveCommand}"
                            FontAttributes="Bold"
                            FontSize="16"
                            HeightRequest="48"
                            IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                            Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Adding Friend...|Add Friend'}"
                            TextColor="White" />

                        <!--  Loading Indicator  -->
                        <ActivityIndicator
                            Margin="0,8"
                            IsRunning="{Binding IsWorking}"
                            IsVisible="{Binding IsWorking}"
                            Color="{AppThemeBinding Light=#004f98,
                                                    Dark={StaticResource Gray300}}" />

                    </VerticalStackLayout>
                </Border>
            </VerticalStackLayout>
        </ScrollView>

        <!--  Loading State Overlay  -->
        <Grid
            Grid.RowSpan="3"
            Background="{StaticResource OverlayColor}"
            IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                Background="{StaticResource LoadingBackgroundColor}"
                HorizontalOptions="Center"
                Stroke="{StaticResource Gray500Brush}"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Adding friend, please wait...!"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>
            </Border>
        </Grid>
    </Grid>
</local:FriendFormViewBase>
