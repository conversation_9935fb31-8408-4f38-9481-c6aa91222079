<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.SignInFormComponent"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    x:DataType="local:SignInFormComponentViewModel"
    Background="{StaticResource OverlayColor}">

    <Grid
        Padding="16"
        Background="{StaticResource OverlayColor}"
        RowDefinitions="1*, Auto, 2*">
        <Border
            x:Name="MainBorder"
            Grid.Row="1"
            Padding="16"
            Background="{StaticResource CardBackgroundColor}"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>

            <ScrollView>
                <VerticalStackLayout Padding="16" Spacing="20">

                    <!--  Header Section with Icon  -->
                    <VerticalStackLayout Margin="0,20,0,10" Spacing="12">
                        <!--  Header Icon  -->
                        <Border
                            Background="{StaticResource SurfaceColor}"
                            HeightRequest="64"
                            HorizontalOptions="Center"
                            WidthRequest="64">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="32" />
                            </Border.StrokeShape>
                            <Image
                                HeightRequest="32"
                                HorizontalOptions="Center"
                                Source="user_group_simple_solid.svg"
                                VerticalOptions="Center"
                                WidthRequest="32" />
                        </Border>

                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="24"
                            HorizontalTextAlignment="Center"
                            Text="Welcome back"
                            TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                        Dark={StaticResource Gray300}}" />
                        <Label
                            FontFamily="Poppins"
                            FontSize="14"
                            HorizontalTextAlignment="Center"
                            Opacity="0.8"
                            Text="Sign in to your account to continue"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray300}}" />
                    </VerticalStackLayout>

                    <!--  Error Display  -->
                    <Border
                        Background="{StaticResource ErrorBackgroundColor}"
                        IsVisible="{Binding HasError}"
                        Stroke="{StaticResource ErrorBorderColor}"
                        StrokeThickness="1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <Grid Padding="12" ColumnDefinitions="Auto,*">
                            <Path
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                Data="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                Fill="#DC2626"
                                VerticalOptions="Start" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Sign in failed"
                                    TextColor="Red" />
                                <Label
                                    FontSize="12"
                                    Text="{Binding Error}"
                                    TextColor="Red" />
                            </VerticalStackLayout>
                        </Grid>
                    </Border>

                    <!--  Sign In Form  -->
                    <VerticalStackLayout x:Name="FormContainer" Spacing="16">

                        <!--  Username Field  -->
                        <VerticalStackLayout>
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="Username"
                                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                            Dark={StaticResource Gray300}}" />
                            <Grid>

                                <Entry
                                    Background="Transparent"
                                    FontSize="16"
                                    Placeholder="Enter your username"
                                    PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                       Dark={StaticResource Gray700}}"
                                    Text="{Binding SelectedItem.NickName}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />

                                <Image
                                    HorizontalOptions="End"
                                    Source="user_group_simple_solid.svg"
                                    WidthRequest="24" />
                            </Grid>
                        </VerticalStackLayout>

                        <!--  Password Field  -->
                        <VerticalStackLayout Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="Password"
                                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                            Dark={StaticResource Gray300}}" />
                            <Grid>

                                <Entry
                                    Background="Transparent"
                                    FontSize="16"
                                    IsPassword="{Binding SelectedItem.IsPasswordHidden, FallbackValue=True}"
                                    Placeholder="Enter your password"
                                    PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                       Dark={StaticResource Gray700}}"
                                    Text="{Binding SelectedItem.PassKey}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />

                                <Image
                                    HorizontalOptions="End"
                                    Source="{Binding SelectedItem.ShowPassword, Converter={StaticResource BoolToStringConverter}, ConverterParameter='lock_keyhole_solid.svg|gear_light.svg'}"
                                    WidthRequest="24">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding TogglePasswordVisibilityCommand}" />
                                    </Image.GestureRecognizers>
                                </Image>

                            </Grid>
                        </VerticalStackLayout>

                        <!--  Enhanced Sign In Button  -->
                        <Button Command="{Binding SaveCommand}" TextColor="White">
                            <Button.ImageSource>
                                <FileImageSource File="lock_icon.svg" />
                            </Button.ImageSource>
                            <Button.Text>
                                <Binding Path="IsWorking">
                                    <Binding.Converter>
                                        <StaticResource Key="BoolToStringConverter" />
                                    </Binding.Converter>
                                    <Binding.ConverterParameter>Signing in...|Sign in</Binding.ConverterParameter>
                                </Binding>
                            </Button.Text>
                        </Button>



                        <!--  Sign Up Link  -->
                        <HorizontalStackLayout
                            Margin="0,20,0,0"
                            HorizontalOptions="Center"
                            Spacing="5">
                            <Button
                                BackgroundColor="Transparent"
                                Clicked="GoToSignup"
                                FontAttributes="Bold"
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Go Back"
                                TextColor="{StaticResource Blue600}" />
                        </HorizontalStackLayout>
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </ScrollView>
        </Border>
        <Grid
            Grid.RowSpan="3"
            Background="{StaticResource OverlayColor}"
            IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                Background="{StaticResource LoadingBackgroundColor}"
                HorizontalOptions="Center"
                Stroke="{StaticResource Gray500Brush}"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Working, please wait...!"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>
            </Border>
        </Grid>
    </Grid>
</ContentPage>