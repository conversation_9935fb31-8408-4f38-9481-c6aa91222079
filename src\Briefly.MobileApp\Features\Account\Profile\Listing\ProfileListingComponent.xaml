<?xml version="1.0" encoding="utf-8" ?>
<local:ProfileListingViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.ProfileListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="My Profile"
    x:DataType="local:ProfileListingView"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.NavBarIsVisible="False">

    <Grid BackgroundColor="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}" VerticalOptions="Fill">

        <ScrollView VerticalOptions="Fill">

            <VerticalStackLayout Spacing="8">
                <Border
                    Margin="16"
                    BackgroundColor="WhiteSmoke"
                    Stroke="Gainsboro"
                    StrokeThickness="0.5">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="16" />
                    </Border.StrokeShape>
                    <VerticalStackLayout Padding="16,24" Spacing="16">

                        <!--  Avatar and Display Name  -->
                        <VerticalStackLayout HorizontalOptions="Center" Spacing="12">
                            <!--  Avatar Display  -->
                            <Border
                                BackgroundColor="{Binding Item.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                                HeightRequest="120"
                                HorizontalOptions="Center"
                                Stroke="#004f98"
                                StrokeThickness="4"
                                WidthRequest="120">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="60" />
                                </Border.StrokeShape>
                                <Grid>
                                    <Image
                                        Aspect="AspectFill"
                                        IsVisible="{Binding Item.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                        Source="{Binding Item.AvatarData}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="36"
                                        HorizontalOptions="Center"
                                        IsVisible="{Binding Item.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                        Text="{Binding Item.DisplayName, Converter={StaticResource InitialsConverter}}"
                                        TextColor="#004f98"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>

                            <!--  Display Name and Description  -->
                            <VerticalStackLayout HorizontalOptions="Center" Spacing="8">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="24"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Item.DisplayName, TargetNullValue='No Name'}"
                                    TextColor="#111827" />
                                <Label
                                    Margin="8,0"
                                    FontSize="14"
                                    HorizontalTextAlignment="Center"
                                    LineBreakMode="WordWrap"
                                    MaxLines="3"
                                    Text="This name will be visible to your friends when you add them as your friend, they can change your name in their contact list also"
                                    TextColor="#6B7280" />
                            </VerticalStackLayout>
                        </VerticalStackLayout>
                    </VerticalStackLayout>
                </Border>

                <!--  Update Profile  -->
                <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                    <Border
                        Grid.Column="0"
                        BackgroundColor="#eff3ff"
                        HeightRequest="40"
                        Stroke="#6084fa"
                        VerticalOptions="Center"
                        WidthRequest="40">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="20" />
                        </Border.StrokeShape>
                        <Image HeightRequest="20" WidthRequest="20">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf007;"
                                    Size="20"
                                    Color="{AppThemeBinding Light=#1E40AF,
                                                            Dark=#3B82F6}" />
                            </Image.Source>
                        </Image>
                    </Border>

                    <VerticalStackLayout
                        Grid.Column="1"
                        Margin="12,0"
                        Spacing="2"
                        VerticalOptions="Center">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Update Profile"
                            TextColor="#111827" />
                        <Label
                            FontSize="14"
                            Text="Edit your profile information and avatar"
                            TextColor="#6B7280" />
                    </VerticalStackLayout>

                    <Image
                        Grid.Column="2"
                        HeightRequest="12"
                        WidthRequest="12">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="JellySolid"
                                Glyph="&#xf105;"
                                Color="{AppThemeBinding Light=#9CA3AF,
                                                        Dark=#9CA3AF}" />
                        </Image.Source>
                    </Image>


                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding UpdateProfileCommand}" />
                    </Grid.GestureRecognizers>
                </Grid>

                <!--  Change Password  -->
                <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                    <Border
                        Grid.Column="0"
                        BackgroundColor="#FEF2F2"
                        HeightRequest="40"
                        Stroke="#f87171"
                        VerticalOptions="Center"
                        WidthRequest="40">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="20" />
                        </Border.StrokeShape>
                        <Image HeightRequest="20" WidthRequest="20">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf023;"
                                    Size="20"
                                    Color="{AppThemeBinding Light=#b91c1c,
                                                            Dark=#ef4444}" />
                            </Image.Source>
                        </Image>
                    </Border>

                    <VerticalStackLayout
                        Grid.Column="1"
                        Margin="12,0"
                        Spacing="2"
                        VerticalOptions="Center">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Change Password"
                            TextColor="#111827" />
                        <Label
                            FontSize="14"
                            Text="Update your account password"
                            TextColor="#6B7280" />
                    </VerticalStackLayout>

                    <Image
                        Grid.Column="2"
                        HeightRequest="12"
                        WidthRequest="12">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="JellySolid"
                                Glyph="&#xf105;"
                                Color="{AppThemeBinding Light=#9CA3AF,
                                                        Dark=#9CA3AF}" />
                        </Image.Source>
                    </Image>

                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding ChangePasswordCommand}" />
                    </Grid.GestureRecognizers>
                </Grid>

                <!--  Referral Codes  -->
                <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                    <Border
                        Grid.Column="0"
                        BackgroundColor="#ecfdf8"
                        HeightRequest="40"
                        Stroke="#34d3a2"
                        VerticalOptions="Center"
                        WidthRequest="40">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="20" />
                        </Border.StrokeShape>
                        <Image HeightRequest="20" WidthRequest="20">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf1e0;"
                                    Size="20"
                                    Color="{AppThemeBinding Light=#059669,
                                                            Dark=#10b981}" />
                            </Image.Source>
                        </Image>
                    </Border>

                    <VerticalStackLayout
                        Grid.Column="1"
                        Margin="12,0"
                        Spacing="2"
                        VerticalOptions="Center">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Manage Referral Codes"
                            TextColor="#111827" />
                        <Label
                            FontSize="14"
                            Text="Create and share friend invitation codes"
                            TextColor="#6B7280" />
                    </VerticalStackLayout>

                    <Image
                        Grid.Column="2"
                        HeightRequest="12"
                        WidthRequest="12">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="JellySolid"
                                Glyph="&#xf105;"
                                Color="{AppThemeBinding Light=#9CA3AF,
                                                        Dark=#9CA3AF}" />
                        </Image.Source>
                    </Image>


                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToReferralCodesCommand}" />
                    </Grid.GestureRecognizers>
                </Grid>

                <!--  Logout  -->
                <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto">
                    <Border
                        Grid.Column="0"
                        BackgroundColor="#fcf6f0"
                        HeightRequest="40"
                        Stroke="#e6b48b"
                        VerticalOptions="Center"
                        WidthRequest="40">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="20" />
                        </Border.StrokeShape>
                        <Image HeightRequest="20" WidthRequest="20">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf08b;"
                                    Size="20"
                                    Color="{AppThemeBinding Light=#d47440,
                                                            Dark=#f97316}" />
                            </Image.Source>
                        </Image>
                    </Border>

                    <VerticalStackLayout
                        Grid.Column="1"
                        Margin="12,0"
                        Spacing="2"
                        VerticalOptions="Center">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Logout"
                            TextColor="#111827" />
                        <Label
                            FontSize="14"
                            Text="Sign out of your account"
                            TextColor="#6B7280" />
                    </VerticalStackLayout>

                    <Image
                        Grid.Column="2"
                        HeightRequest="12"
                        WidthRequest="12">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="JellySolid"
                                Glyph="&#xf105;"
                                Color="{AppThemeBinding Light=#9CA3AF,
                                                        Dark=#9CA3AF}" />
                        </Image.Source>
                    </Image>


                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding LogoutCommand}" />
                    </Grid.GestureRecognizers>
                </Grid>
            </VerticalStackLayout>

        </ScrollView>

        <!--  Loading State  -->
        <Grid
            Grid.RowSpan="3"
            Background="{StaticResource OverlayColor}"
            IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                Background="{StaticResource LoadingBackgroundColor}"
                HorizontalOptions="Center"
                Stroke="{StaticResource Gray500Brush}"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Working, please wait...!"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>
            </Border>
        </Grid>
        <Border
            Grid.Row="2"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray800}}"
            StrokeThickness="0.5"
            VerticalOptions="End" />
    </Grid>
</local:ProfileListingViewBase>
