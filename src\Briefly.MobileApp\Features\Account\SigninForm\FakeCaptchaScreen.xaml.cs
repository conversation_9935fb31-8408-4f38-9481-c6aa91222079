using CommunityToolkit.Mvvm.Input;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using DeepMessage.ServiceContracts.Features.Configurations;
using Microsoft.Extensions.DependencyInjection;
using ModelFury.Briefly.MobileApp.Features.Account;
using Platform.Client.Services.Services;
using Platform.Framework.Core;
using Plugin.Firebase.CloudMessaging;
using System.Security.Claims;
using System.Text.Json;
using System.Windows.Input;

namespace ModelFury.Briefly.MobileApp.Features.Account
{
    public class FakeCaptchaScreenBase : FormBaseMaui<SignInFormBusinessObject, SignInFormViewModel, string, ISignInFormDataService>
    {
        public FakeCaptchaScreenBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
        {
        }
    }

    public partial class FakeCaptchaScreen : FakeCaptchaScreenBase
    {
        private IClientEncryptionService _encryptionService;
        private ISecureKeyManager _secureKeyManager;
        private ILocalStorageService _storageService;

        // UI State Properties - Focus management removed, using platform defaults

        private bool hasError;
        public bool HasError
        {
            get => hasError;
            set
            {
                hasError = value;
                OnPropertyChanged();
            }
        }

        public string CaptchaImage { get; }

        public bool IsUserNotRegistered => !IsUserRegistered;

        // Dynamic content properties
        private bool isUserRegistered;
        public bool IsUserRegistered
        {
            get => isUserRegistered;
            set
            {
                isUserRegistered = value;
                OnPropertyChanged();
                UpdateDynamicContent();
            }
        }

        private string? nickname;
        public string? NickName
        {
            get => nickname;
            set
            {
                nickname = value;
                IsUserRegistered = !string.IsNullOrEmpty(value);
                OnPropertyChanged();
            }
        }

        public string ObfuscatedUsername => ObfuscateUsername(NickName ?? "");

        public string HeaderMessage => IsUserRegistered 
            ? "Captcha Verification" 
            : "Activate Application";

        public string SubHeaderMessage => IsUserRegistered 
            ? "Enter your verification code to continue" 
            : "Activate using Purchase code";

        public string AuthCodeLabel => IsUserRegistered 
            ? "Verification Code" 
            : "Activation Code";

        public string AuthCodePlaceholder => IsUserRegistered 
            ? "Enter verification code" 
            : "Enter activation code";

        public string SubmitButtonText => IsUserRegistered 
            ? "Verify" 
            : "Activate";

        public ICommand ResetRegistrationCommand { get; }
        public ICommand GoToLoginCommand { get; }
        public ICommand RefreshCaptchaCommand { get; }
        public ICommand AudioCaptchaCommand { get; }

        public FakeCaptchaScreen(IServiceScopeFactory scopeFactory) : base(scopeFactory, null!)
        {
            InitializeComponent();

            ResetRegistrationCommand = new AsyncRelayCommand(ResetRegistration);
            GoToLoginCommand = new AsyncRelayCommand(GoToLogin);
            RefreshCaptchaCommand = new RelayCommand(RefreshCaptcha);
            AudioCaptchaCommand = new RelayCommand(PlayAudioCaptcha);
            CaptchaImage = $"c{Random.Shared.Next(1,4)}.jpg";
            // Initialize services
            using var scope = scopeFactory.CreateScope();
            _encryptionService = scope.ServiceProvider.GetRequiredService<IClientEncryptionService>();
            _secureKeyManager = scope.ServiceProvider.GetRequiredService<ISecureKeyManager>();
            _storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            try
            {
                NickName = Task.Run(() => _storageService.GetValue(ClaimTypes.Name)).Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading nickname: {ex.Message}");
            }

            BindingContext = this;

            // Set initial state for entrance animation
            var mainBorder = this.FindByName<Border>("MainBorder");
            if (mainBorder != null)
            {
                mainBorder.Opacity = 0;
                mainBorder.Scale = 0.9;
                mainBorder.TranslationY = 30;
            }
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing(); 
            // Animate entrance
            await AnimateEntrance();
        }

        /// <summary>
        /// Animates the entrance of the fake captcha screen
        /// </summary>
        private async Task AnimateEntrance()
        {
            try
            {
                var mainBorder = this.FindByName<Border>("MainBorder");
                if (mainBorder != null)
                {
                    // Small delay to ensure the page is loaded
                    await Task.Delay(100);

                    // Animate entrance with fade, scale, and slide up
                    var fadeTask = mainBorder.FadeTo(1, 500, Easing.CubicOut);
                    var scaleTask = mainBorder.ScaleTo(1, 500, Easing.CubicOut);
                    var translateTask = mainBorder.TranslateTo(0, 0, 500, Easing.CubicOut);

                    await Task.WhenAll(fadeTask, scaleTask, translateTask);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Entrance animation failed: {ex.Message}");
                // Ensure visibility if animation fails
                var mainBorder = this.FindByName<Border>("MainBorder");
                if (mainBorder != null)
                {
                    mainBorder.Opacity = 1;
                    mainBorder.Scale = 1;
                    mainBorder.TranslationY = 0;
                }
            }
        }

        protected override async Task<SignInFormViewModel> CreateSelectedItem()
        {
            var defaultPass = string.Empty;
#if DEBUG
            defaultPass = "Cancel55%%";
#endif

            return await Task.FromResult(new SignInFormViewModel
            {
                PassKey = defaultPass,
                NickName = NickName,
                DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
            });
        }
         

        private void UpdateDynamicContent()
        {
            OnPropertyChanged(nameof(HeaderMessage));
            OnPropertyChanged(nameof(SubHeaderMessage));
            OnPropertyChanged(nameof(AuthCodeLabel));
            OnPropertyChanged(nameof(AuthCodePlaceholder));
            OnPropertyChanged(nameof(SubmitButtonText));
            OnPropertyChanged(nameof(ObfuscatedUsername));
            OnPropertyChanged(nameof(IsUserNotRegistered));
        }

        private async Task ResetRegistration()
        {
            try
            {
                // Show loading state
                IsBusy = true;

                // Clear stored data
                await _storageService.RemoveValue(ClaimTypes.Name);
                await _storageService.RemoveValue(ClaimTypes.NameIdentifier);
                await _storageService.RemoveValue("pub2e_");
                await _storageService.RemoveValue("pub1o_");
                await _storageService.RemoveValue("auth_token");
                await _storageService.RemoveValue("refresh_token");
                await _storageService.RemoveValue("code");

                // Reset state
                NickName = null;
                SelectedItem.NickName = null;
                SelectedItem.PassKey = string.Empty;
                HasError = false;


                IsBusy = false;

                await DisplayAlert("Success", "Registration has been reset. You can now enter a new activation code.", "OK");
            }
            catch (Exception ex)
            {
                IsBusy = false;
                HandleError($"Error resetting registration: {ex.Message}");
            }
        }

        private async Task GoToLogin()
        {
            // Add haptic feedback for better user experience
            try
            {
#if ANDROID || IOS
                HapticFeedback.Default.Perform(HapticFeedbackType.Click);
#endif
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Haptic feedback failed: {ex.Message}");
            }

            await AnimateButtonTap();
            await AnimateToLogin();
        }

        /// <summary>
        /// Animates the button tap feedback
        /// </summary>
        private async Task AnimateButtonTap()
        {
            try
            {
                // Find the "Click here" label and animate it
                var clickHereLabel = this.FindByName<Label>("ClickHereLabel");
                if (clickHereLabel != null)
                {
                    // Quick scale animation for tap feedback
                    await clickHereLabel.ScaleTo(0.95, 100, Easing.CubicOut);
                    await clickHereLabel.ScaleTo(1, 100, Easing.CubicIn);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Button tap animation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Animates the transition from fake captcha to login screen with fade effect
        /// </summary>
        private async Task AnimateToLogin()
        {
            try
            {
                // Disable user interaction during animation
                this.IsEnabled = false;

                // Find the main border for more targeted animation
                var mainBorder = this.FindByName<Border>("MainBorder");

                if (mainBorder != null)
                {
                    // Animate the main content with more sophisticated effects
                    var fadeTask = mainBorder.FadeTo(0, 400, Easing.CubicOut);
                    var scaleTask = mainBorder.ScaleTo(0.9, 400, Easing.CubicOut);
                    var translateTask = mainBorder.TranslateTo(0, 50, 400, Easing.CubicOut);

                    // Also fade the background
                    var backgroundFadeTask = this.FadeTo(0.3, 400, Easing.CubicOut);

                    // Wait for all animations to complete
                    await Task.WhenAll(fadeTask, scaleTask, translateTask, backgroundFadeTask);
                }
                else
                {
                    // Fallback to whole page animation
                    var fadeTask = this.FadeTo(0, 400, Easing.CubicOut);
                    var scaleTask = this.ScaleTo(0.95, 400, Easing.CubicOut);
                    await Task.WhenAll(fadeTask, scaleTask);
                }

                // Navigate to login screen
                await Shell.Current.GoToAsync("//login");

                // Pop the modal after navigation
                await Navigation.PopModalAsync();
            }
            catch (Exception ex)
            {
                // Fallback to direct navigation if animation fails
                System.Diagnostics.Debug.WriteLine($"Animation failed, using fallback navigation: {ex.Message}");
                this.IsEnabled = true;
                this.Opacity = 1;
                this.Scale = 1;
                await Shell.Current.GoToAsync("//login");
                await Navigation.PopModalAsync();
            }
        }

        private void HandleError(string error)
        {
            HasError = !string.IsNullOrEmpty(error);
             
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (!string.IsNullOrEmpty(error))
                {
                    await DisplayAlert("Error", error, "OK");
                }
            });
        }

        // Captcha functionality
        private void RefreshCaptcha()
        {
            // Placeholder for captcha refresh functionality
            // In a real implementation, this would reload the captcha image
        }

        private void PlayAudioCaptcha()
        {
            // Placeholder for audio captcha functionality
            // In a real implementation, this would play an audio version of the captcha
        }

        public override async Task OnAfterSaveAsync(string authResult)
        {
            try
            { 
                if (authResult == "Authenticate Online")
                {
                    var scope = ScopeFactory.CreateScope();
                    var signInClientService = scope.ServiceProvider.GetRequiredKeyedService<ISignInFormDataService>("client");
                    var selectedBusinessObject = ConvertViewModelToBusinessModel(SelectedItem);
                    
                    if (!string.IsNullOrEmpty(selectedBusinessObject.NickName))
                        selectedBusinessObject.PassKey = $"{selectedBusinessObject.NickName}{_encryptionService.GenerateBitSignature(SelectedItem.PassKey!)}!";
           
                    authResult = await signInClientService.SaveAsync(selectedBusinessObject);
                    
                    
                    if (authResult == "Register")
                    {
                        // Handle signup flow
                        await _storageService.SetValue(SelectedItem.PassKey!, "code");
                        await Shell.Current.GoToAsync("//signup");
                        await Navigation.PopModalAsync();
                        return;
                    } 
                }

                var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(authResult);

                if (authClaims != null)
                {
                    await StoreAuthenticationTokens(authClaims);
                    await _secureKeyManager.DeriveAndStoreKeysAsync(SelectedItem.NickName!, SelectedItem.PassKey!);
                    
                    // Notify authentication state provider
                    //using var scope = ScopeFactory.CreateScope();
                    //var authStateProvider = scope.ServiceProvider.GetRequiredService<CustomAuthStateProvider>();
                    //authStateProvider.NotifyUserAuthentication(authClaims.UserId, authClaims.Username);

                    //await RegisterDevice();
                    await Shell.Current.GoToAsync("//messages");
                    await Navigation.PopModalAsync();
                }
            }
            catch (Exception ex)
            {
                HandleError("Invalid Code.");
                System.Diagnostics.Debug.WriteLine($"Error processing authentication: {ex.Message}");
            }
        }

        private async Task StoreAuthenticationTokens(AuthorizationClaimsModel authClaims)
        {
            try
            {
                await _storageService.SetValue(authClaims.Token, "auth_token");
                await _storageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
                await _storageService.SetValue(authClaims.Username, ClaimTypes.Name);
                await _storageService.SetValue(authClaims.Pub1, "pub1o_");
                await _storageService.SetValue(authClaims.Pub2, "pub2e_");

                // Sync user profile data including avatar
                var scope = ScopeFactory.CreateScope();
                var profileSyncService = scope.ServiceProvider.GetRequiredService<Platform.Client.Services.Features.Account.IProfileSyncService>();
                await profileSyncService.SyncUserProfileAsync(authClaims.UserId, authClaims.Username);

                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var user = await context.ApplicationUsers.FindAsync(authClaims.UserId);
                if (user == null)
                {
                    user = new ApplicationUser()
                    {
                        Id = authClaims.UserId,
                        NickName = authClaims.Username,
                        Hash = "dummy",
                    };
                    context.ApplicationUsers.Add(user);
                }
                
                user.Pub1 = authClaims.Pub1;
                user.Pub2 = authClaims.Pub2;
                user.DisplayName = authClaims.DisplayName;
                user.AvatarData = authClaims.AvatarData;
                user.AvatarDescription = authClaims.AvatarDescription;
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log error but don't throw as authentication was successful
                System.Diagnostics.Debug.WriteLine($"Error storing authentication tokens: {ex.Message}");
            }
        }

        private async Task RegisterDevice()
        {
            var deviceId = await _storageService.GetValue("device_id");
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = Guid.NewGuid().ToString();
                await _storageService.SetValue(deviceId, "device_id");
            }

            var deviceToken = await _storageService.GetValue("device_token");
            if (string.IsNullOrEmpty(deviceToken))
            {
                try
                {
                    var scope = ScopeFactory.CreateScope();
                    var fcm = scope.ServiceProvider.GetRequiredService<IFirebaseCloudMessaging>();
                    deviceToken = await fcm.GetTokenAsync();
                    await _storageService.SetValue(deviceToken, "device_token");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error getting FCM token: {ex.Message}");
                    deviceToken = "unavailable";
                }
            }

            var deviceTokenRegistration = await _storageService.GetValue("device_token_registration");
            if (string.IsNullOrEmpty(deviceTokenRegistration))
            {
                try
                {
                    var scope = ScopeFactory.CreateScope();
                    var deviceTokenFormDataService = scope.ServiceProvider.GetRequiredService<IDeviceTokenFormDataService>();
                    await deviceTokenFormDataService.SaveAsync(new DeviceTokenFormBusinessObject()
                    {
                        Id = deviceId,
                        DeviceToken = deviceToken,
                        DeviceName = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
                        Platform = DeviceInfo.Platform.ToString(),
                    });

                    await _storageService.SetValue("true", "device_token_registration");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error registering device token: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Obfuscates the username to show only last 2-3 characters
        /// </summary>
        private string ObfuscateUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
                return string.Empty;

            if (username.Length <= 2)
                return new string('*', username.Length);

            // Show last 2 characters for usernames 3-5 chars, last 3 for longer
            int visibleChars = username.Length <= 5 ? 2 : 3;
            int hiddenChars = username.Length - visibleChars;

            return new string('*', hiddenChars) + username.Substring(hiddenChars);
        }
    }
}