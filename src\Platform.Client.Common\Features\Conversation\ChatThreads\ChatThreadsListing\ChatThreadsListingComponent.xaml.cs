﻿using Android.Views;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiApp.Services;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Platform;
using Platform.Client.Services.Features.Conversation;
using Platform.Client.Services.Services;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Input;
namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadsListingViewBase : ListingBaseMaui<ChatThreadsListingViewModel, ChatThreadsListingBusinessObject,
                                            ChatThreadsFilterViewModel, ChatThreadsFilterBusinessObject, IChatThreadsListingDataService>
{
    public ChatThreadsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class ChatThreadsListingView : ChatThreadsListingViewBase, IDisposable
{
    private readonly ISecureKeyManager secureKeyManager;
    private readonly IServiceScope _serviceScope;
    private readonly ILogger<ChatThreadsListingView>? _logger;
    private RSA? rsaKey;

    public ChatThreadsListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        // Create a properly managed service scope
        _serviceScope = scopeFactory.CreateScope();
        secureKeyManager = _serviceScope.ServiceProvider.GetRequiredService<ISecureKeyManager>();

        // Get logger for debugging (optional)
        _logger = _serviceScope.ServiceProvider.GetService<ILogger<ChatThreadsListingView>>();

        InitializeComponent();

        MessageTappedCommand = new Command<ChatThreadsListingViewModel>(async (p) =>
        {
            //if (chats.ContainsKey(p.Id))
            //{
            //    var chat = chats[p.Id];
            //    if (Navigation.NavigationStack.Contains(chat))
            //    {
            //        // pop until it’s the top page
            //        while (Navigation.NavigationStack.Last() != chat)
            //            await Navigation.PopAsync(false);
            //    }
            //    else
            //    {
            //        // otherwise push it
            //        await Navigation.PushAsync(chat, false);
            //    }


            //}
            //else
            //{
            //    var chat = new ChatMessagesListingView(scopeFactory, p.Id, p.Name, p.Avatar);
            //    chats.Add(p.Id, chat);
            //    await Navigation.PushAsync(chat);
            //}
            var chat = new ChatMessagesListingView(scopeFactory, p.Id, p.Name, p.Avatar);
            await Navigation.PushAsync(chat, false);
        });

        PubSub.Hub.Default.Subscribe<string>((m) =>
        {
            if (m == "NewMessageReceived")
            { 
                _ = LoadItems(false);
            }
        });

        WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, (r, m) =>
        {
            _ = LoadItems(false);
        });
        BindingContext = this;
    }

    protected override void OnAppearing()
    { 
        // Initialize RSA key for decryption (matches Razor implementation)
        InitializeRSAKey();

        base.OnAppearing();
    }

    /// <summary>
    /// Initializes the RSA key for message decryption
    /// Matches the pattern from ChatThreadsListing.razor.cs OnInitializedAsync
    /// </summary>
    private void InitializeRSAKey()
    {
        try
        {
            if (secureKeyManager.IsRSAKeyAvailable())
            {
                rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
                _logger?.LogDebug("RSA key initialized successfully for chat threads listing");
            }
            else
            {
                _logger?.LogWarning("RSA key not available during initialization for chat threads listing");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to initialize RSA key for chat threads listing");
        }
    }

    /// <summary>
    /// Refreshes the RSA key if it becomes unavailable
    /// Useful for handling authentication state changes
    /// </summary>
    private void RefreshRSAKeyIfNeeded()
    {
        try
        {
            if (rsaKey == null && secureKeyManager.IsRSAKeyAvailable())
            {
                rsaKey = secureKeyManager.GetRSAPrivateKeyAsync();
                _logger?.LogDebug("RSA key refreshed for chat threads listing");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to refresh RSA key for chat threads listing");
        }
    }

    /// <summary>
    /// Converts business objects to view models with decrypted last messages
    /// Matches the exact implementation from ChatThreadsListing.razor.cs ConvertToListViewModel
    /// </summary>
    protected override ChatThreadsListingViewModel[] ConvertListingBusinessItemsToListingViewModelItems(List<ChatThreadsListingBusinessObject> listBusinessObjects)
    {
        return listBusinessObjects.Where(x => x != null).Select(x => new ChatThreadsListingViewModel
        {
            Id = x!.Id,
            Avatar = x.Avatar,
            Name = x.Name,
            LastMessage = DecryptLastMessage(x.LastMessage),
            LastMessageTime = x.LastMessageTime
        }).ToArray();
    }

    /// <summary>
    /// Decrypts last message content using RSA private key from secure memory
    /// Matches the exact implementation from ChatThreadsListing.razor.cs and ChatMessagesListingComponent
    /// </summary>
    private string DecryptLastMessage(string? encryptedContent)
    {
        if (string.IsNullOrEmpty(encryptedContent))
            return string.Empty;

        try
        {
            // Check if RSA key is available in memory
            if (!secureKeyManager.IsRSAKeyAvailable())
            {
                // If no key available, return placeholder indicating authentication needed
                return "[Authentication required to decrypt message]";
            }

            // Try to refresh RSA key if not available
            if (rsaKey == null)
            {
                RefreshRSAKeyIfNeeded();
            }

            // Use stored RSA key if available, otherwise get a new one
            RSA? keyToUse = rsaKey;
            bool shouldDisposeKey = false;

            if (keyToUse == null)
            {
                keyToUse = secureKeyManager.GetRSAPrivateKeyAsync();
                shouldDisposeKey = true;

                if (keyToUse == null)
                {
                    return "[Private Key Not Available]";
                }
            }

            try
            {
                // Decrypt the message content
                var encryptedBytes = Convert.FromBase64String(encryptedContent);
                var decryptedBytes = keyToUse.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
                return Encoding.UTF8.GetString(decryptedBytes);
            }
            finally
            {
                // Only dispose if we created a new key instance
                if (shouldDisposeKey)
                {
                    keyToUse?.Dispose();
                }
            }
        }
        catch (FormatException ex)
        {
            _logger?.LogWarning(ex, "Invalid Base64 format for encrypted last message in chat threads listing");
            return "[Invalid message format]";
        }
        catch (CryptographicException ex)
        {
            _logger?.LogWarning(ex, "Failed to decrypt last message in chat threads listing");
            return "[Failed to decrypt message]";
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Unexpected error decrypting last message in chat threads listing");
            return "[Failed to decrypt message]";
        }
    } 

    public ICommand MessageTappedCommand { get; set; }

    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(async () =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = true;
                });

                using (var scope = ScopeFactory.CreateScope())
                {
                    Error = string.Empty;
                    try
                    {
                        var listingService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var items = await listingService.GetItemsAsync() ?? throw new Exception("Paginated items object is null");
                        foreach (var conversationItem in items)
                        {
                            var conversation = await context.Conversations.FirstOrDefaultAsync(x => x.Id == conversationItem.Id);

                            if (conversation == null)
                            {
                                conversation = new DeepMessage.Client.Common.Data.Conversation()
                                {
                                    Id = conversationItem.Id,
                                    CreatedAt = conversationItem.CreatedAt,
                                    IsDeleted = conversationItem.IsDeleted,
                                    Type = conversationItem.Type,
                                    Title = conversationItem.Title

                                };
                                context.Conversations.Add(conversation);
                                await context.SaveChangesAsync();
                            }

                            ArgumentNullException.ThrowIfNull(conversationItem.ChatParticipents);

                            foreach (var participentItem in conversationItem.ChatParticipents)
                            {
                                var participent = await context.ConversationParticipants.FirstOrDefaultAsync(x => x.Id == participentItem.Id);
                                if (participent == null)
                                {
                                    participent = new ConversationParticipant()
                                    {
                                        Id = participentItem.Id,
                                        ConversationId = participentItem.ConversationId,
                                        IsAdmin = participentItem.IsAdmin,
                                        JoinedAt = participentItem.JoinedAt,
                                        UserId = participentItem.UserId,
                                    };
                                    context.ConversationParticipants.Add(participent);
                                    await context.SaveChangesAsync();
                                }
                            }
                        }

                        await LoadItems(true);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        _ = Shell.Current.GoToAsync("//signin");
                    }

                    catch (Exception ex)
                    {
                        //Crashes.TrackError(ex);
                        if (ex.Message.Contains("invalid_token"))
                        {
                            _ = Shell.Current.GoToAsync("//signin");
                        }
                        Error = ex.Message;
                    }
                }


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = false;
                });
            });
        }
    }

    /// <summary>
    /// Disposes of resources including RSA key and service scope
    /// Matches disposal pattern from ChatMessagesListingComponent and ChatThreadsListing.razor.cs
    /// </summary>
    public void Dispose()
    {
        try
        {
            // Dispose RSA key
            rsaKey?.Dispose();
            rsaKey = null;

            // Dispose service scope
            _serviceScope?.Dispose();

            _logger?.LogDebug("ChatThreadsListingView disposed");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error during disposal of ChatThreadsListingView");
        }
    }
}




