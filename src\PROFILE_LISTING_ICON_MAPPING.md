# ProfileListingView Icon Mapping Documentation

## Overview
This document details the replacement of 4 SVG icons in ProfileListingComponent.xaml with FontImageSource elements using the Jelly font family, including specific color mappings extracted from the original SVG files.

## Icon Replacements

### **1. Update Profile Icon**
**Location**: Line 92-100 in ProfileListingComponent.xaml

**Original SVG**: `user_chisel_regular_full.svg`
- **Original Color**: `#1E40AF` (Blue 700)
- **SVG Content**: User profile icon with blue fill

**FontImageSource Replacement**:
```xml
<FontImageSource
    FontFamily="Jelly"
    Glyph="&#xf007;"
    Size="20"
    Color="{AppThemeBinding Light=#1E40AF, Dark=#3B82F6}" />
```

**Color Mapping**:
- **Light Mode**: `#1E40AF` (Original blue from SVG)
- **Dark Mode**: `#3B82F6` (Softer blue variant for better dark theme visibility)

**Semantic**: User icon (&#xf007;) - Represents user profile/account management

---

### **2. Change Password Icon**
**Location**: Line 142-150 in ProfileListingComponent.xaml

**Original SVG**: `lock_chisel_regular_full.svg`
- **Original Color**: `#b91c1c` (Red 700)
- **SVG Content**: Lock icon with red fill

**FontImageSource Replacement**:
```xml
<FontImageSource
    FontFamily="Jelly"
    Glyph="&#xf023;"
    Size="20"
    Color="{AppThemeBinding Light=#b91c1c, Dark=#ef4444}" />
```

**Color Mapping**:
- **Light Mode**: `#b91c1c` (Original red from SVG)
- **Dark Mode**: `#ef4444` (Softer red variant for better dark theme visibility)

**Semantic**: Lock icon (&#xf023;) - Represents security/password management

---

### **3. Referral Codes Icon**
**Location**: Line 192-200 in ProfileListingComponent.xaml

**Original SVG**: `share_nodes_chisel_regular_full.svg`
- **Original Color**: `#059669` (Green 600)
- **SVG Content**: Share/network nodes icon with green fill

**FontImageSource Replacement**:
```xml
<FontImageSource
    FontFamily="Jelly"
    Glyph="&#xf1e0;"
    Size="20"
    Color="{AppThemeBinding Light=#059669, Dark=#10b981}" />
```

**Color Mapping**:
- **Light Mode**: `#059669` (Original green from SVG)
- **Dark Mode**: `#10b981` (Softer green variant for better dark theme visibility)

**Semantic**: Share/Network icon (&#xf1e0;) - Represents sharing and referral functionality

---

### **4. Logout Icon**
**Location**: Line 242-250 in ProfileListingComponent.xaml

**Original SVG**: `logout_chisel_regular_full.svg`
- **Original Color**: `#d47440` (Orange 600)
- **SVG Content**: Logout/sign-out icon with orange fill

**FontImageSource Replacement**:
```xml
<FontImageSource
    FontFamily="Jelly"
    Glyph="&#xf2f5;"
    Size="20"
    Color="{AppThemeBinding Light=#d47440, Dark=#f97316}" />
```

**Color Mapping**:
- **Light Mode**: `#d47440` (Original orange from SVG)
- **Dark Mode**: `#f97316` (Softer orange variant for better dark theme visibility)

**Semantic**: Sign-out icon (&#xf2f5;) - Represents logout/sign-out functionality

## Color Strategy

### **Light Mode Colors**
All light mode colors use the **exact original colors** extracted from the SVG files:
- **Blue**: `#1E40AF` (User profile)
- **Red**: `#b91c1c` (Security/password)
- **Green**: `#059669` (Sharing/referral)
- **Orange**: `#d47440` (Logout/exit)

### **Dark Mode Colors**
Dark mode colors use **softer variants** of the same color families for better visibility and reduced eye strain:
- **Blue**: `#3B82F6` (Lighter blue for better contrast)
- **Red**: `#ef4444` (Lighter red for better contrast)
- **Green**: `#10b981` (Lighter green for better contrast)
- **Orange**: `#f97316` (Lighter orange for better contrast)

## Implementation Benefits

### **1. Color Consistency**
- Maintains the original visual intent of each icon
- Preserves the color-coded functionality (blue=profile, red=security, green=sharing, orange=logout)
- Provides appropriate dark mode variants for accessibility

### **2. Theme Support**
- Automatic theme switching with `AppThemeBinding`
- Optimized colors for both light and dark themes
- Better accessibility and readability in all lighting conditions

### **3. Performance**
- Font-based icons load faster than SVG files
- Better caching and memory usage
- Reduced app bundle size

### **4. Maintainability**
- Consistent font family (Jelly) across all icons
- Easy to update globally if needed
- Simplified asset management

## Preserved Functionality

All existing functionality has been maintained:
- **Command Bindings**: All TapGestureRecognizer commands preserved
- **Layout**: Icon sizing and positioning unchanged
- **Visual Hierarchy**: Border backgrounds and strokes maintained
- **Accessibility**: Icon semantics preserved through appropriate glyph selection

## Testing Checklist

- [x] Icons display correctly in light theme with original colors
- [x] Icons display correctly in dark theme with softer variants
- [x] Theme switching updates icon colors appropriately
- [x] All command bindings continue to work
- [x] Icon sizing and layout remain consistent
- [x] No binding errors in IDE diagnostics
- [x] Visual hierarchy and color coding preserved

## Color Reference Table

| Icon | Function | Light Color | Dark Color | Jelly Glyph |
|------|----------|-------------|------------|-------------|
| User | Update Profile | `#1E40AF` | `#3B82F6` | `&#xf007;` |
| Lock | Change Password | `#b91c1c` | `#ef4444` | `&#xf023;` |
| Share | Referral Codes | `#059669` | `#10b981` | `&#xf1e0;` |
| Logout | Sign Out | `#d47440` | `#f97316` | `&#xf2f5;` |

This color mapping ensures visual consistency while providing optimal user experience across both light and dark themes.
