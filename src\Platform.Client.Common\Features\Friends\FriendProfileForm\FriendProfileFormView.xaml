<?xml version="1.0" encoding="utf-8" ?>
<local:FriendProfileFormViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendProfileFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Edit Friend"
    x:DataType="local:FriendProfileFormViewBase"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.NavBarIsVisible="False">

    <Grid>
        <!--  Content Section  -->
        <ScrollView Grid.Row="1">
            <VerticalStackLayout Spacing="0">

                <!--  Error Message  -->
                <Border
                    Margin="16,16,16,0"
                    BackgroundColor="#FEF2F2"
                    IsVisible="{Binding HasError}"
                    Stroke="#FECACA"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Margin="0,2,8,0"
                            HeightRequest="20"
                            VerticalOptions="Start"
                            WidthRequest="20">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf06a;"
                                    Size="20"
                                    Color="#DC2626" />
                            </Image.Source>
                        </Image>
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            Text="{Binding Error}"
                            TextColor="#DC2626" />
                    </Grid>
                </Border>

                <!--  Profile Display Section  -->
                <Border
                    Margin="0,16,0,0"
                    BackgroundColor="{AppThemeBinding Light=White, Dark={StaticResource Gray700}}"
                    StrokeThickness="0">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="0" />
                    </Border.StrokeShape>
                    <Grid
                        Padding="16"
                        ColumnDefinitions="Auto,*"
                        RowDefinitions="Auto">

                        <!--  Avatar Display  -->
                        <Border
                            Grid.Column="0"
                            BackgroundColor="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Transparent|#E5E7EB'}"
                            HeightRequest="100"
                            Stroke="#004f98"
                            StrokeThickness="3"
                            VerticalOptions="Start"
                            WidthRequest="100">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="50" />
                            </Border.StrokeShape>
                            <Grid>
                                <Image
                                    Aspect="AspectFill"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                    Source="{Binding SelectedItem.AvatarData}" />
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="32"
                                    HorizontalOptions="Center"
                                    IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                    Text="{Binding SelectedItem.FriendName, Converter={StaticResource InitialsConverter}}"
                                    TextColor="#004f98"
                                    VerticalOptions="Center" />
                            </Grid>
                        </Border>

                        <!--  Name and Info  -->
                        <VerticalStackLayout
                            Grid.Column="1"
                            Margin="16,0,0,0"
                            Spacing="4"
                            VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="20"
                                LineBreakMode="TailTruncation"
                                Text="{Binding SelectedItem.FriendName, TargetNullValue='No Name'}"
                                TextColor="{AppThemeBinding Light=#111827, Dark={StaticResource Gray300}}" />
                            <Label
                                FontSize="14"
                                Text="{Binding SelectedItem.Tagline, TargetNullValue='No tagline'}"
                                TextColor="{AppThemeBinding Light=#6B7280, Dark={StaticResource Gray400}}" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>

                <!--  Form Fields Section  -->
                <VerticalStackLayout
                    Margin="0,8,0,0"
                    Padding="16"
                    BackgroundColor="{AppThemeBinding Light=White, Dark={StaticResource Gray700}}"
                    Spacing="24">

                    <!--  Avatar Selection  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Select Avatar"
                            TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Choose from predefined avatar images"
                            TextColor="{AppThemeBinding Light=#6B7280, Dark={StaticResource Gray400}}" />

                        <Picker
                            Title="Choose an avatar..."
                            FontSize="14"
                            ItemsSource="{Binding AvatarOptions}"
                            SelectedItem="{Binding SelectedItem.AvatarData}"
                            TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}"
                            TitleColor="{AppThemeBinding Light=#9CA3AF, Dark={StaticResource Gray500}}" />
                    </VerticalStackLayout>

                    <!--  Friend Name  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Friend Name"
                            TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            LineBreakMode="WordWrap"
                            Text="This is how your friend will appear in your contact list"
                            TextColor="{AppThemeBinding Light=#6B7280, Dark={StaticResource Gray400}}" />
                        <Entry
                            BackgroundColor="Transparent"
                            FontSize="16"
                            Placeholder="Enter friend's name"
                            PlaceholderColor="{AppThemeBinding Light=#9CA3AF, Dark={StaticResource Gray500}}"
                            Text="{Binding SelectedItem.FriendName}"
                            TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}" />
                    </VerticalStackLayout>

                    <!--  Tagline/Description  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontSize="16"
                            Text="Tagline"
                            TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}" />
                        <Label
                            Margin="0,0,0,8"
                            FontSize="12"
                            Text="Add a personal note or description for this friend"
                            TextColor="{AppThemeBinding Light=#6B7280, Dark={StaticResource Gray400}}" />
                        <Border
                            BackgroundColor="{AppThemeBinding Light=#F9FAFB, Dark={StaticResource Gray600}}"
                            HeightRequest="80"
                            Stroke="{AppThemeBinding Light=#E5E7EB, Dark={StaticResource Gray500}}"
                            StrokeThickness="1">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="8" />
                            </Border.StrokeShape>
                            <Editor
                                Margin="12,8"
                                AutoSize="TextChanges"
                                BackgroundColor="Transparent"
                                FontSize="16"
                                MaxLength="500"
                                Placeholder="Enter a tagline or description..."
                                PlaceholderColor="{AppThemeBinding Light=#9CA3AF, Dark={StaticResource Gray500}}"
                                Text="{Binding SelectedItem.Tagline}"
                                TextColor="{AppThemeBinding Light=#374151, Dark={StaticResource Gray300}}" />
                        </Border>
                    </VerticalStackLayout>

                    <!--  Loading Indicator  -->
                    <ActivityIndicator
                        Margin="0,16"
                        IsRunning="{Binding IsWorking}"
                        IsVisible="{Binding IsWorking}"
                        Color="{AppThemeBinding Light=#004f98, Dark={StaticResource Gray300}}" />

                </VerticalStackLayout>

                <Button
                    Margin="48,16"
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                    Text="{Binding IsWorking, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Saving...|Save Changes'}" />
            </VerticalStackLayout>
        </ScrollView>

        <Button
            Margin="8"
            Padding="8"
            Background="Transparent"
            HeightRequest="40"
            HorizontalOptions="End"
            VerticalOptions="Start"
            WidthRequest="40">

            <Button.ImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf00d;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700}, Dark={StaticResource Gray300}}" />
            </Button.ImageSource>
        </Button>
    </Grid>
</local:FriendProfileFormViewBase>
